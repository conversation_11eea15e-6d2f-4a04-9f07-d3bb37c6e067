using System;
using UnityEngine;

namespace Fish.PlayMaker
{
    public class PlayMakerUnitTest : MonoBehaviour
    {
        #region 按钮

        public Button btnAudio;
        public Button btnAvatar;
        public Button btnCamera;
        public Button btnCommon;
        public Button btnDataStore;
        public Button btnEvent;
        public Button btnGameEffect;
        public Button btnGameObject;
        public Button btnJson;
        public Button btnNetwork;
        public Button btnReflect;
        public Button btnTextMesh;
        public Button btnUI;
        #endregion
        
        private void Awake()
        {
            BindUIEvent();
        }

        private void BindUIEvent()
        {
        }
        
        public void OnBtnAudio()
        {
        }
        
        public void OnBtnAvatar()
        {
        }
        
    }
}