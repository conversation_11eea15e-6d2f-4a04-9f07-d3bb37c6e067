using System;
using System.Reflection;
using HutongGames.PlayMaker;
using UnityEngine;

namespace Fish.PlayMaker
{
	[ActionCategory("Fish/Common")]
	public class GameEffectRollingNumberAction : FsmStateAction
	{
		[HutongGames.PlayMaker.Tooltip("目标GameObject的key")]
		public string TargetKey;

		[HutongGames.PlayMaker.Tooltip("起始数值的key，默认值：0")]
		public string PreGainKey = "PreGainKey";
		[HutongGames.PlayMaker.Tooltip("目标数值的key，默认值：0")]
		public string GainKey = "GainKey";
		[HutongGames.PlayMaker.Tooltip("滚动时间的key，默认值：2.0秒")]
		public string RollTimeKey = "RollTimeKey";
		[HutongGames.PlayMaker.Tooltip("结束缩放时间的key，默认值：1.0秒")]
		public string EndScaleTimeKey = "EndScaleTimeKey";
		[HutongGames.PlayMaker.Tooltip("是否在滚动结束时闪烁的key，默认值：false")]
		public string IsRollingEndFlashKey = "IsRollingEndFlashKey";
		[HutongGames.PlayMaker.Tooltip("是否改变颜色的key，默认值：true")]
		public string IsChangeColorKey = "IsChangeColorKey";
		[HutongGames.PlayMaker.Tooltip("是否添加X符号的key，默认值：false")]
		public string IsAddXKey = "IsAddXKey";
		[HutongGames.PlayMaker.Tooltip("是否添加加号的key，默认值：false")]
		public string IsAddPlusKey = "IsAddPlusKey";
		[HutongGames.PlayMaker.Tooltip("是否添加逗号分隔符的key，默认值：true")]
		public string IsAddCommaKey = "IsAddCommaKey";
		[HutongGames.PlayMaker.Tooltip("结束缩放比例的key，默认值：2.0")]
		public string EndScaleKey = "EndScaleKey";
		[HutongGames.PlayMaker.Tooltip("是否连续的key，默认值：false")]
		public string IsContinuousKey = "IsContinuousKey";
		[HutongGames.PlayMaker.Tooltip("膨胀数字类型的key(0默认 1膨胀 2不膨胀)，默认值：0")]
		public string ExpendNumberTypeKey = "ExpendNumberTypeKey";
		[HutongGames.PlayMaker.Tooltip("数字类型的key(0未定义 1金币 2分数 3连击)，默认值：0")]
		public string NumberTypeKey = "NumberTypeKey";

		public override void OnEnter()
		{
			var target = Owner.gameObject.GetDataObject(TargetKey) as GameObject;
			if (target == null)
			{
				GameLogger.LogWarning($"target is null,key is {TargetKey},owner is {Owner.gameObject.name},instanceID:{Owner.gameObject.GetInstanceID()}");
				Finish();
				return;
			}

			var className = "GameEffectRollingNumber";
			var ret = ILRManager.Instance.InvokeMethod(null, "HotGame.BhvManager", "GetBehaviourInChildren", PlayMakerUtility.GetBindingFlag(new BindingFlags[] { BindingFlags.Static, BindingFlags.Public }), false, new object[] { target.transform, className }, new Type[] { typeof(Transform), typeof(string) });
			if (ret == null)
			{
				GameLogger.LogWarning("GameEffectRollingNumber is null");
				Finish();
				return;
			}

			long preGain = Owner.gameObject.GetDataObjectWithDefault<long>(PreGainKey, 0);
			long gain = Owner.gameObject.GetDataObjectWithDefault<long>(GainKey, 0);
			float rollTime = Owner.gameObject.GetDataObjectWithDefault<float>(RollTimeKey, 2f);
			float endScaleTime = Owner.gameObject.GetDataObjectWithDefault<float>(EndScaleTimeKey, 1f);
			bool isRollingEndFlash = Owner.gameObject.GetDataObjectWithDefault<bool>(IsRollingEndFlashKey, false);
			bool isChangeColor = Owner.gameObject.GetDataObjectWithDefault<bool>(IsChangeColorKey, true);
			bool isAddX = Owner.gameObject.GetDataObjectWithDefault<bool>(IsAddXKey, false);
			bool isAddPlus = Owner.gameObject.GetDataObjectWithDefault<bool>(IsAddPlusKey, false);
			bool isAddComma = Owner.gameObject.GetDataObjectWithDefault<bool>(IsAddCommaKey, true);
			float endScale = Owner.gameObject.GetDataObjectWithDefault<float>(EndScaleKey, 2f);
			bool isContinuous = Owner.gameObject.GetDataObjectWithDefault<bool>(IsContinuousKey, false);
			int expendNumberType = Owner.gameObject.GetDataObjectWithDefault<int>(ExpendNumberTypeKey, 0);
			int numberType = Owner.gameObject.GetDataObjectWithDefault<int>(NumberTypeKey, 0);

			var flags = new BindingFlags[] { BindingFlags.Instance, BindingFlags.Public };
			
			var fullClassName = "HotGame.GameEffectRollingNumber";

			ILRManager.Instance.SetFieldValue(ret, fullClassName, "preGain", preGain, PlayMakerUtility.GetBindingFlag(flags), false);
			ILRManager.Instance.SetFieldValue(ret, fullClassName, "gain", gain, PlayMakerUtility.GetBindingFlag(flags), false);
			ILRManager.Instance.SetFieldValue(ret, fullClassName, "rollTime", rollTime, PlayMakerUtility.GetBindingFlag(flags), false);
			ILRManager.Instance.SetFieldValue(ret, fullClassName, "endScaleTime", endScaleTime, PlayMakerUtility.GetBindingFlag(flags), false);
			ILRManager.Instance.SetFieldValue(ret, fullClassName, "isRollingEndFlash", isRollingEndFlash, PlayMakerUtility.GetBindingFlag(flags), false);
			ILRManager.Instance.SetFieldValue(ret, fullClassName, "isChangeColor", isChangeColor, PlayMakerUtility.GetBindingFlag(flags), false);
			ILRManager.Instance.SetFieldValue(ret, fullClassName, "isAddX", isAddX, PlayMakerUtility.GetBindingFlag(flags), false);
			ILRManager.Instance.SetFieldValue(ret, fullClassName, "isAddPlus", isAddPlus, PlayMakerUtility.GetBindingFlag(flags), false);
			ILRManager.Instance.SetFieldValue(ret, fullClassName, "isAddComma", isAddComma, PlayMakerUtility.GetBindingFlag(flags), false);
			ILRManager.Instance.SetFieldValue(ret, fullClassName, "endScale", endScale, PlayMakerUtility.GetBindingFlag(flags), false);
			ILRManager.Instance.SetFieldValue(ret, fullClassName, "_isContinuous", isContinuous, PlayMakerUtility.GetBindingFlag(flags), false);
			ILRManager.Instance.SetFieldValue(ret, fullClassName, "expendNumberType", expendNumberType, PlayMakerUtility.GetBindingFlag(flags), false);
			ILRManager.Instance.SetFieldValue(ret, fullClassName, "numberType", numberType, PlayMakerUtility.GetBindingFlag(flags), false);

			// 调用init接口
			ILRManager.Instance.InvokeMethod(ret, fullClassName, "Init", PlayMakerUtility.GetBindingFlag(flags), false, new object[] { preGain, gain, rollTime, isRollingEndFlash }, new Type[] { typeof(long), typeof(long), typeof(float), typeof(bool) });

			Finish();
		}
	}
}