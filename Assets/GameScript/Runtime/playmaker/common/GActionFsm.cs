using System;
using HutongGames.PlayMaker;
using UnityEngine;

namespace Fish.PlayMaker
{
	[ActionCategory("Fish/Common")]
	[HutongGames.PlayMaker.Tooltip("GAction实现")]
	public class GActionFsm : FsmStateAction
	{
		[HutongGames.PlayMaker.Tooltip("是否使用Store中的target")]
		public bool UseStoreTarget;
		[HutongGames.PlayMaker.Tooltip("目标对象")]
		public FsmOwnerDefault targetObject;
		[HutongGames.PlayMaker.Tooltip("Store中的target的key")]
		public string StoreTargetKey;

		public PMGAction Action;
		[HutongGames.PlayMaker.Tooltip("动作完成时触发的事件")]
		public FsmEvent finishedEvent;

		private GAction _currentAction;
		private GameObject _targetGameObject;

		public override void Reset()
		{
			targetObject = null;
			_currentAction = null;
			Action = new PMGAction();
		}

		public override void OnEnter()
		{
			if (UseStoreTarget)
			{
				_targetGameObject = Owner.gameObject.GetDataObject(StoreTargetKey) as GameObject;
			}
			else
			{
				_targetGameObject = Fsm.GetOwnerDefaultTarget(targetObject);
			}

			if (_targetGameObject == null)
			{
				GameLogger.LogWarning("Target object is null!");
				Finish();
				return;
			}

			_currentAction = CreateAction(Action);

			if (_currentAction != null)
			{
				_currentAction.StartWithTarget(_targetGameObject);
				// 先更新一次，触发一下instant类型的action逻辑
				_currentAction.Step(0);

				// 这里需要再次检查是否为空，因为_currentAction.Step以后，如果Fsm的Owner被隐藏了，
				// 会走OnExit，在那里会清空_currentAction，所以这里需要再次判断
				if (_currentAction != null)
				{
					if (_currentAction.IsDone())
					{
						ActionFinished();
					}
				}
			}
			else
			{
				GameLogger.LogWarning("Failed to create GAction for type: " + Action.ActionType);
				Finish();
			}
		}

		public override void OnUpdate()
		{
			if (_currentAction != null)
			{
				_currentAction.Step(Time.deltaTime);

				if (_currentAction.IsDone())
				{
					ActionFinished();
				}
			}
		}

		private void ActionFinished()
		{
			if (finishedEvent != null)
			{
				Fsm.Event(finishedEvent);
			}
			if (_currentAction != null)
			{
				_currentAction.Stop();
			}
			_currentAction = null;

			Finish();
		}

		public override void OnExit()
		{
			if (_currentAction != null)
			{
				_currentAction.Stop();
				_currentAction = null;
			}
		}

		private GAction CreateAction(PMGAction action)
		{
			var type = action.ActionType;
			if (type == ActionType.Show)
			{
				return GAction.Show();
			}
			else if (type == ActionType.Hide)
			{
				return GAction.Hide();
			}
			else if (type == ActionType.RemoveSelf)
			{
				return GAction.RemoveSelf();
			}
			else if (type == ActionType.FlipX)
			{
				return GAction.FlipX();
			}
			else if (type == ActionType.FlipY)
			{
				return GAction.FlipY();
			}
			else if (type == ActionType.CallFunc)
			{
				//需要都从Owner中获取
				var param = action.ParamCallFunc;
				return GAction.SendFsmEvent(Owner.gameObject, param.fsmName.GetValue(Owner.gameObject), param.eventName.GetValue(Owner.gameObject));
			}
			else if (type == ActionType.DelayTime)
			{
				var param = action.ParamDelayTime;
				return GAction.DelayTime(param.duration.GetValue(Owner.gameObject));
			}
			else if (type == ActionType.MoveBy)
			{
				var param = action.ParamMoveBy;
				return GAction.MoveBy(param.duration.GetValue(Owner.gameObject), param.deltaPosition.GetValue(Owner.gameObject), param.worldPosition.GetValue(Owner.gameObject));
			}
			else if (type == ActionType.MoveTo)
			{
				var param = action.ParamMoveTo;
				return GAction.MoveTo(param.duration.GetValue(Owner.gameObject), param.target.GetValue(Owner.gameObject), param.worldPosition.GetValue(Owner.gameObject));
			}
			else if (type == ActionType.RotateBy)
			{
				var param = action.ParamRotateBy;
				return GAction.RotateBy(param.duration.GetValue(Owner.gameObject), param.deltaAngle.GetValue(Owner.gameObject));
			}
			else if (type == ActionType.RotateTo)
			{
				var param = action.ParamRotateTo;
				return GAction.RotateTo(param.duration.GetValue(Owner.gameObject), param.destAngle.GetValue(Owner.gameObject));
			}
			else if (type == ActionType.RotateBy3D)
			{
				var param = action.ParamRotateBy3D;
				var rotation = param.DeltaAngle.GetValue(Owner.gameObject);
				return GAction.RotateBy3d(param.duration.GetValue(Owner.gameObject), rotation.x, rotation.y, rotation.z);
			}
			else if (type == ActionType.ScaleTo)
			{
				var param = action.ParamScaleTo;
				var scale = param.Scale.GetValue(Owner.gameObject);
				return GAction.ScaleTo(param.duration.GetValue(Owner.gameObject), scale.x, scale.y, scale.z);
			}
			else if (type == ActionType.ScaleBy)
			{
				var param = action.ParamScaleBy;
				var value = param.Scale.GetValue(Owner.gameObject);

				return GAction.ScaleBy(param.duration.GetValue(Owner.gameObject), value.x, value.y, value.z);
			}
			else if (type == ActionType.FadeTo)
			{
				var param = action.ParamFadeTo;
				return GAction.FadeTo(param.duration.GetValue(Owner.gameObject), param.alpha.GetValue(Owner.gameObject));
			}
			else if (type == ActionType.TintTo)
			{
				var param = action.ParamTintTo;
				var color = param.Color.GetValue(Owner.gameObject);
				return GAction.TintTo(param.duration.GetValue(Owner.gameObject), color.r, color.g, color.b);
			}
			else if (type == ActionType.BlendTo)
			{
				var param = action.ParamBlendTo;
				var alpha = param.alpha.GetValue(Owner.gameObject);
				return GAction.BlendTo(param.duration.GetValue(Owner.gameObject), alpha);
			}
			else if (type == ActionType.Flash)
			{
				return GAction.Flash();
			}
			else if (type == ActionType.FlashWithCurve)
			{
				var param = action.ParamFlashWithCurve;
				var curve = param.curve.GetValue(Owner.gameObject);
				var useDefaultCurve = param.useDefaultCurve.GetValue(Owner.gameObject);
				if (useDefaultCurve)
					curve = null;
				return GAction.FlashWithCurve(curve, param.duration.GetValue(Owner.gameObject));
			}
			else if (type == ActionType.BezierTo)
			{
				var param = action.ParamBezierTo;
				return GAction.BezierTo(param.duration.GetValue(Owner.gameObject), param.p0.GetValue(Owner.gameObject), param.p1.GetValue(Owner.gameObject), param.p2.GetValue(Owner.gameObject), param.p3.GetValue(Owner.gameObject));
			}
			else if (type == ActionType.CanvasGroupAlphaFadeTo)
			{
				var param = action.ParamCanvasGroupAlphaFadeTo;
				return GAction.CanvasGroupAlphaFadeTo(param.duration.GetValue(Owner.gameObject), param.alpha.GetValue(Owner.gameObject));
			}
			else if (type == ActionType.Sequence)
			{
				var param = action.ParamSequence;
				GAction[] actions = new GAction[param.actions.Length];
				for (int i = 0; i < param.actions.Length; i++)
				{
					actions[i] = CreateAction(param.actions[i]);
				}
				return GAction.Sequence(actions);
			}
			else if (type == ActionType.Spawn)
			{
				var param = action.ParamSpawn;
				GAction[] actions = new GAction[param.actions.Length];
				for (int i = 0; i < param.actions.Length; i++)
				{
					actions[i] = CreateAction(param.actions[i]);
				}
				return GAction.Spawn(actions);
			}
			else if (type == ActionType.Repeat)
			{
				var param = action.ParamRepeate;
				var subAction = CreateAction(param.action[0]);
				return GAction.Repeat(subAction, (uint)param.times.GetValue(Owner.gameObject));
			}
			else if (type == ActionType.RepeatForever)
			{
				var parma = action.ParamRepeatForever;
				var subAction = CreateAction(parma.action[0]);
				return GAction.RepeatForever(subAction as GActionInterval);
			}
			return null;
		}

		public enum ActionType
		{
			Show,
			Hide,
			RemoveSelf,
			FlipX,
			FlipY,
			CallFunc,
			DelayTime,
			MoveBy,
			MoveTo,
			RotateBy,
			RotateTo,
			RotateBy3D,
			ScaleTo,
			ScaleBy,
			FadeTo,
			TintTo,
			BlendTo,
			Flash,
			BezierTo,
			CanvasGroupAlphaFadeTo,
			Sequence,
			Spawn,
			Repeat,
			RepeatForever,
			FlashWithCurve,
		}
	}

	[Serializable]
	public class PMGAction
	{
		public GActionFsm.ActionType ActionType;

		// 直接序列化各种参数类型，避免多态序列化问题
		public PMGActionDelayTime ParamDelayTime;
		public PMGActionMoveTo ParamMoveTo;
		public PMGActionMoveBy ParamMoveBy;
		public PMGActionRotateBy ParamRotateBy;
		public PMGActionRotateTo ParamRotateTo;
		public PMGActionRotateBy3D ParamRotateBy3D;
		public PMGActionScaleTo ParamScaleTo;
		public PMGActionScaleBy ParamScaleBy;
		public PMGActionFadeTo ParamFadeTo;
		public PMGActionTintTo ParamTintTo;
		public PMGActionBlendTo ParamBlendTo;
		public PMGActionBezierTo ParamBezierTo;
		public PMGActionCanvasGroupAlphaFadeTo ParamCanvasGroupAlphaFadeTo;
		public PMGActionSequence ParamSequence;
		public PMGActionRepeat ParamRepeate;
		public PMGActionRepeatForever ParamRepeatForever;
		public PMGActionSpawn ParamSpawn;
		public PMGActionCallFunc ParamCallFunc;
		public PMGActionFlashWithCurve ParamFlashWithCurve;

		[NonSerialized]
		public bool foldout = true;

		public void OnActionTypeChanged(GActionFsm.ActionType newActionType)
		{
			ActionType = newActionType;

			// 清空所有参数
			Reset();

			// 根据类型创建对应的参数对象
			switch (newActionType)
			{
				case GActionFsm.ActionType.DelayTime:
					ParamDelayTime = new PMGActionDelayTime().Init();
					break;
				case GActionFsm.ActionType.MoveTo:
					ParamMoveTo = new PMGActionMoveTo().Init();
					break;
				case GActionFsm.ActionType.MoveBy:
					ParamMoveBy = new PMGActionMoveBy().Init();
					break;
				case GActionFsm.ActionType.RotateBy:
					ParamRotateBy = new PMGActionRotateBy().Init();
					break;
				case GActionFsm.ActionType.RotateTo:
					ParamRotateTo = new PMGActionRotateTo().Init();
					break;
				case GActionFsm.ActionType.RotateBy3D:
					ParamRotateBy3D = new PMGActionRotateBy3D().Init();
					break;
				case GActionFsm.ActionType.ScaleTo:
					ParamScaleTo = new PMGActionScaleTo().Init();
					break;
				case GActionFsm.ActionType.ScaleBy:
					ParamScaleBy = new PMGActionScaleBy().Init();
					break;
				case GActionFsm.ActionType.FadeTo:
					ParamFadeTo = new PMGActionFadeTo().Init();
					break;
				case GActionFsm.ActionType.TintTo:
					ParamTintTo = new PMGActionTintTo().Init();
					break;
				case GActionFsm.ActionType.BlendTo:
					ParamBlendTo = new PMGActionBlendTo().Init();
					break;
				case GActionFsm.ActionType.BezierTo:
					ParamBezierTo = new PMGActionBezierTo().Init();
					break;
				case GActionFsm.ActionType.CanvasGroupAlphaFadeTo:
					ParamCanvasGroupAlphaFadeTo = new PMGActionCanvasGroupAlphaFadeTo().Init();
					break;
				case GActionFsm.ActionType.Sequence:
					ParamSequence = new PMGActionSequence().Init();
					break;
				case GActionFsm.ActionType.Repeat:
					ParamRepeate = new PMGActionRepeat().Init();
					break;
				case GActionFsm.ActionType.RepeatForever:
					ParamRepeatForever = new PMGActionRepeatForever().Init();
					break;
				case GActionFsm.ActionType.Spawn:
					ParamSpawn = new PMGActionSpawn().Init();
					break;
				case GActionFsm.ActionType.CallFunc:
					ParamCallFunc = new PMGActionCallFunc().Init();
					break;
				case GActionFsm.ActionType.FlashWithCurve:
					ParamFlashWithCurve = new PMGActionFlashWithCurve().Init();
					break;
			}
		}

		private void Reset()
		{
			ParamFlashWithCurve = null;
			ParamDelayTime = null;
			ParamMoveTo = null;
			ParamMoveBy = null;
			ParamRotateBy = null;
			ParamRotateTo = null;
			ParamRotateBy3D = null;
			ParamScaleTo = null;
			ParamScaleBy = null;
			ParamFadeTo = null;
			ParamTintTo = null;
			ParamBlendTo = null;
			ParamBezierTo = null;
			ParamCanvasGroupAlphaFadeTo = null;
			ParamSequence = null;
			ParamRepeate = null;
			ParamRepeatForever = null;
			ParamSpawn = null;
			ParamCallFunc = null;
			ParamFlashWithCurve = null;
		}
	}

	[Serializable]
	public class PMGActionDelayTime
	{
		public PMParam<float> duration;

		public PMGActionDelayTime Init()
		{
			duration = new PMParam<float>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionMoveBy
	{
		public PMParam<float> duration;
		public PMParam<Vector3> deltaPosition;
		public PMParam<bool> worldPosition;

		public PMGActionMoveBy Init()
		{
			duration = new PMParam<float>();
			deltaPosition = new PMParam<Vector3>();
			worldPosition = new PMParam<bool>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionMoveTo
	{
		public PMParam<float> duration;
		public PMParam<Vector3> target;
		public PMParam<bool> worldPosition;

		public PMGActionMoveTo Init()
		{
			duration = new PMParam<float>();
			target = new PMParam<Vector3>();
			worldPosition = new PMParam<bool>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionRotateBy
	{
		public PMParam<float> duration;
		public PMParam<float> deltaAngle;

		public PMGActionRotateBy Init()
		{
			duration = new PMParam<float>();
			deltaAngle = new PMParam<float>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionRotateTo
	{
		public PMParam<float> duration;
		public PMParam<float> destAngle;

		public PMGActionRotateTo Init()
		{
			duration = new PMParam<float>();
			destAngle = new PMParam<float>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionRotateBy3D
	{
		public PMParam<float> duration;
		public PMParam<Vector3> DeltaAngle;

		public PMGActionRotateBy3D Init()
		{
			duration = new PMParam<float>();
			DeltaAngle = new PMParam<Vector3>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionScaleTo
	{
		public PMParam<float> duration;
		public PMParam<Vector3> Scale;
		public PMGActionScaleTo Init()
		{
			duration = new PMParam<float>();
			Scale = new PMParam<Vector3>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionScaleBy
	{
		public PMParam<float> duration;
		public PMParam<Vector3> Scale;

		public PMGActionScaleBy Init()
		{
			duration = new PMParam<float>();
			Scale = new PMParam<Vector3>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionFadeTo
	{
		public PMParam<float> duration;
		public PMParam<float> alpha;

		public PMGActionFadeTo Init()
		{
			duration = new PMParam<float>();
			alpha = new PMParam<float>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionTintTo
	{
		public PMParam<float> duration;
		public PMParam<Color> Color;

		public PMGActionTintTo Init()
		{
			duration = new PMParam<float>();
			Color = new PMParam<Color>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionBlendTo
	{
		public PMParam<float> duration;
		public PMParam<float> alpha;

		public PMGActionBlendTo Init()
		{
			duration = new PMParam<float>();
			alpha = new PMParam<float>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionBezierTo
	{
		public PMParam<float> duration;
		public PMParam<Vector3> p0;
		public PMParam<Vector3> p1;
		public PMParam<Vector3> p2;
		public PMParam<Vector3> p3;

		public PMGActionBezierTo Init()
		{
			duration = new PMParam<float>();
			p0 = new PMParam<Vector3>();
			p1 = new PMParam<Vector3>();
			p2 = new PMParam<Vector3>();
			p3 = new PMParam<Vector3>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionCanvasGroupAlphaFadeTo
	{
		public PMParam<float> duration;
		public PMParam<float> alpha;

		public PMGActionCanvasGroupAlphaFadeTo Init()
		{
			duration = new PMParam<float>();
			alpha = new PMParam<float>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionRepeat
	{
		public PMGAction[] action;
		public PMParam<int> times;

		public PMGActionRepeat Init()
		{
			action = new PMGAction[1];
			action[0] = new PMGAction();
			times = new PMParam<int>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionRepeatForever
	{
		public PMGAction[] action;

		public PMGActionRepeatForever Init()
		{
			action = new PMGAction[1];
			action[0] = new PMGAction();
			return this;
		}
	}

	[Serializable]
	public class PMGActionSpawn
	{
		public PMGAction[] actions;

		// 编辑器折叠状态（不序列化）
		[NonSerialized]
		public bool foldout = true;

		public PMGActionSpawn Init()
		{
			actions = new PMGAction[0];
			return this;
		}
	}

	[Serializable]
	public class PMGActionSequence
	{
		public PMGAction[] actions;

		// 编辑器折叠状态（不序列化）
		[NonSerialized]
		public bool foldout = true;

		public PMGActionSequence Init()
		{
			actions = new PMGAction[0];
			return this;
		}
	}

	[Serializable]
	public class PMGActionFlashWithCurve
	{
		public PMParam<bool> useDefaultCurve;
		public PMParam<AnimationCurve> curve;
		public PMParam<float> duration;

		public PMGActionFlashWithCurve Init()
		{
			useDefaultCurve = new PMParam<bool>();
			curve = new PMParam<AnimationCurve>();
			curve.RawValue = new AnimationCurve();
			duration = new PMParam<float>();
			return this;
		}
	}

	[Serializable]
	public class PMGActionCallFunc
	{
		public PMParam<string> fsmName;
		public PMParam<string> eventName;

		public PMGActionCallFunc Init()
		{
			fsmName = new PMParam<string>();
			eventName = new PMParam<string>();
			return this;
		}
	}


	[Serializable]
	public class PMParam<T>
	{
		public bool UseStoreData;
		public string StoreKey;
		public T RawValue;


		public T GetValue(GameObject gameObject)
		{
			if (UseStoreData)
			{
				var value = gameObject.GetDataObject(StoreKey);
				return (T)value;
			}
			else
			{
				return RawValue;
			}
		}
	}

}